# SAXS_CRC.py 修改说明

## 修改目的
移除 CuPy 依赖，使用 NumPy 替代，以便于打包和部署。

## 主要修改内容

### 1. 导入语句修改
**修改前：**
```python
import cupy as cp
```

**修改后：**
```python
# 移除了 cupy 导入
```

### 2. SESDC_single 函数修改

#### 变量名更改
**修改前：**
```python
gpu_R, gpu_Y = cp.array(Beta_R)[:, :, np.newaxis], cp.array(Beta_Y)[:, :, np.newaxis]
gpu_V_phi = (4*cp.pi*gpu_R**3/3) * gpu_Y
```

**修改后：**
```python
cpu_R, cpu_Y = Beta_R[:, :, np.newaxis], Beta_Y[:, :, np.newaxis]
cpu_V_phi = (4*np.pi*cpu_R**3/3) * cpu_Y
```

#### 计算函数替换
**修改前：**
```python
U1 = cp.array(U1) * gpu_R
U1 = ( (3/(U1**3)) * (cp.sin(U1)-U1*cp.cos(U1)))**2 * gpu_V_phi
I1 = cp.sum(U1, axis=1)
```

**修改后：**
```python
U1 = U1 * cpu_R
U1 = ( (3/(U1**3)) * (np.sin(U1)-U1*np.cos(U1)))**2 * cpu_V_phi
I1 = np.sum(U1, axis=1)
```

#### 数组操作替换
**修改前：**
```python
I3 = cp.asnumpy(cp.concatenate(I2, axis=1))/Beta_number
```

**修改后：**
```python
I3 = np.concatenate(I2, axis=1)/Beta_number
```

## 性能影响

### 优点
1. **兼容性更好**：不需要 CUDA 环境，可在任何有 NumPy 的系统上运行
2. **部署简单**：减少了复杂的 GPU 依赖
3. **打包容易**：PyInstaller 打包时不会遇到 CuPy 相关问题

### 缺点
1. **计算速度**：CPU 计算比 GPU 计算慢，特别是在大数据集上
2. **内存使用**：可能需要更多内存来处理大型数组

## 建议

### 对于小到中等规模的数据
- 修改后的代码完全可以满足需求
- 性能差异可能不明显

### 对于大规模数据处理
- 如果需要高性能计算，可以考虑：
  1. 使用 Numba 进行 JIT 编译加速
  2. 使用多进程并行计算
  3. 分批处理大数据集

### 可选的性能优化方案
```python
# 可以添加 Numba 装饰器来加速计算密集型函数
from numba import jit

@jit(nopython=True)
def compute_intensity_batch(U1, cpu_R, cpu_V_phi):
    U1 = U1 * cpu_R
    U1 = ((3/(U1**3)) * (np.sin(U1)-U1*np.cos(U1)))**2 * cpu_V_phi
    return np.sum(U1, axis=1)
```

## 验证
修改后的代码已通过语法检查，所有 CuPy 引用已成功替换为 NumPy 等价操作。

## 注意事项
1. 确保系统有足够内存处理大型数组
2. 对于计算密集型任务，考虑使用进度条显示计算进度
3. 可以根据实际需求调整 `count_number` 参数来平衡内存使用和计算效率
