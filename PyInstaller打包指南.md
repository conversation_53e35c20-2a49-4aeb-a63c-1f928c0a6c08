# PyInstaller 打包 Python 程序为 EXE 文件指南

## 简介

PyInstaller 是一个用于将 Python 应用程序打包成独立可执行文件的工具。它支持 Windows、macOS 和 Linux 系统，可以将 Python 脚本及其依赖项打包成一个或多个文件，使得没有安装 Python 的用户也能运行您的程序。

## 安装 PyInstaller

### 使用 pip 安装

```bash
pip install pyinstaller
```

### 验证安装

```bash
pyinstaller --version
```

## 基本使用

### 最简单的打包命令

```bash
pyinstaller your_script.py
```

这将创建一个包含多个文件的 `dist` 文件夹。

### 打包成单个 exe 文件

```bash
pyinstaller --onefile your_script.py
```

### 常用参数说明

| 参数 | 说明 |
|------|------|
| `--onefile` | 打包成单个可执行文件 |
| `--windowed` | 不显示控制台窗口（适用于GUI程序） |
| `--noconsole` | 同 `--windowed` |
| `--icon=icon.ico` | 指定程序图标 |
| `--name=app_name` | 指定生成的exe文件名 |
| `--distpath=./dist` | 指定输出目录 |
| `--workpath=./build` | 指定临时文件目录 |
| `--clean` | 清理临时文件 |

## 实际示例

### 示例 1：简单的控制台程序

假设有一个简单的 Python 脚本 `hello.py`：

```python
def main():
    name = input("请输入您的姓名: ")
    print(f"你好, {name}!")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
```

打包命令：
```bash
pyinstaller --onefile hello.py
```

### 示例 2：GUI 程序（使用 tkinter）

```python
import tkinter as tk
from tkinter import messagebox

def show_message():
    messagebox.showinfo("消息", "Hello, World!")

root = tk.Tk()
root.title("示例程序")
root.geometry("300x200")

button = tk.Button(root, text="点击我", command=show_message)
button.pack(pady=50)

root.mainloop()
```

打包命令：
```bash
pyinstaller --onefile --windowed --icon=app.ico gui_app.py
```

### 示例 3：包含外部文件的程序

如果程序需要读取配置文件、图片等外部资源：

```bash
pyinstaller --onefile --add-data "config.txt;." --add-data "images;images" your_script.py
```

在代码中访问这些文件：

```python
import sys
import os

def resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        # PyInstaller 创建临时文件夹，将路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

# 使用示例
config_file = resource_path('config.txt')
```

## 高级配置

### 创建 spec 文件

对于复杂的打包需求，可以生成 spec 文件进行详细配置：

```bash
pyinstaller --onefile --name=MyApp your_script.py
```

这会生成一个 `MyApp.spec` 文件，您可以编辑它来自定义打包过程。

### spec 文件示例

```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['your_script.py'],
    pathex=[],
    binaries=[],
    datas=[('config.txt', '.'), ('images', 'images')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MyApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app.ico'
)
```

使用 spec 文件打包：
```bash
pyinstaller MyApp.spec
```

## 常见问题和解决方案

### 1. 缺少模块错误

如果运行时提示缺少某个模块，可以使用 `--hidden-import` 参数：

```bash
pyinstaller --onefile --hidden-import=module_name your_script.py
```

### 2. 文件过大

- 使用虚拟环境，只安装必要的包
- 使用 `--exclude-module` 排除不需要的模块
- 考虑使用 UPX 压缩（需要单独安装）

### 3. 启动速度慢

- 避免使用 `--onefile`，改用目录模式
- 减少不必要的导入

### 4. 杀毒软件误报

- 在知名的代码签名机构购买代码签名证书
- 将程序添加到杀毒软件白名单

## 最佳实践

1. **使用虚拟环境**：确保只包含必要的依赖
2. **测试打包结果**：在没有 Python 环境的机器上测试
3. **版本控制**：将 spec 文件加入版本控制
4. **文档化**：记录打包过程和特殊配置
5. **自动化**：使用脚本自动化打包过程

## 打包脚本示例

创建一个 `build.py` 脚本来自动化打包过程：

```python
import os
import shutil
import subprocess

def build_exe():
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 执行打包命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--icon=app.ico',
        '--name=MyApp',
        'main.py'
    ]
    
    subprocess.run(cmd, check=True)
    print("打包完成！")

if __name__ == "__main__":
    build_exe()
```

## 总结

PyInstaller 是一个强大的 Python 打包工具，通过合理的配置和优化，可以创建高质量的可执行文件。记住要在目标环境中充分测试您的应用程序，确保所有功能正常工作。
